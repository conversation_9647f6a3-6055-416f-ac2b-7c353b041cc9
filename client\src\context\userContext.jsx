import React, { createContext, useState, useEffect } from 'react'
import io from 'socket.io-client'

export const UserContext = createContext(null);

const UserProvider = ({ children }) => {
  const [account, setAccount] = useState(null);
  const [person, setPerson] = useState({});
  const [socket, setSocket] = useState(null);
  const [onlineUsers, setOnlineUsers] = useState([]);
  const [messages, setMessages] = useState([]);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('userData');

    if (token && userData) {
      setAccount(JSON.parse(userData));
      setIsAuthenticated(true);

      const newSocket = io('http://localhost:5000');
      setSocket(newSocket);

      newSocket.emit('login', JSON.parse(userData).userName);

      newSocket.on('onlineUsers', (users) => {
        setOnlineUsers(users);
      });

      newSocket.on('receiveMessage', (message) => {
        setMessages(prev => [...prev, message]);
      });

      newSocket.on('loadMessages', (loadedMessages) => {
        setMessages(loadedMessages);
      });

      return () => {
        newSocket.disconnect();
      };
    }
  }, []);

  const login = (userData, token) => {
    localStorage.setItem('token', token);
    localStorage.setItem('userData', JSON.stringify(userData));
    setAccount(userData);
    setIsAuthenticated(true);

    const newSocket = io('http://localhost:5000');
    setSocket(newSocket);
    newSocket.emit('login', userData.userName);

    newSocket.on('onlineUsers', (users) => {
      setOnlineUsers(users);
    });

    newSocket.on('receiveMessage', (message) => {
      setMessages(prev => [...prev, message]);
    });

    newSocket.on('loadMessages', (loadedMessages) => {
      setMessages(loadedMessages);
    });
  };

  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('userData');
    setAccount(null);
    setIsAuthenticated(false);
    if (socket) {
      socket.disconnect();
      setSocket(null);
    }
    setOnlineUsers([]);
    setMessages([]);
  };

  return (
    <UserContext.Provider value={{
      account,
      setAccount,
      person,
      setPerson,
      socket,
      onlineUsers,
      messages,
      setMessages,
      isAuthenticated,
      login,
      logout
    }}>
      {children}
    </UserContext.Provider>
  );
};

export default UserProvider;