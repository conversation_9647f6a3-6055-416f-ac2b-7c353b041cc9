import React, { createContext, useState, useEffect } from 'react'
import socketService from '../api/socket'

export const UserContext = createContext(null);

const UserProvider = ({ children }) => {
  const [account, setAccount] = useState(null);
  const [person, setPerson] = useState({});
  const [socket, setSocket] = useState(null);
  const [onlineUsers, setOnlineUsers] = useState([]);
  const [messages, setMessages] = useState([]);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('userData');

    if (token && userData) {
      setAccount(JSON.parse(userData));
      setIsAuthenticated(true);

      const newSocket = socketService.connect(JSON.parse(userData).userName);
      setSocket(newSocket);

      socketService.onOnlineUsers((users) => {
        setOnlineUsers(users);
      });

      socketService.onReceiveMessage((message) => {
        setMessages(prev => [...prev, message]);
      });

      socketService.onLoadMessages((loadedMessages) => {
        setMessages(loadedMessages);
      });

      return () => {
        socketService.disconnect();
      };
    }
  }, []);

  const login = (userData, token) => {
    localStorage.setItem('token', token);
    localStorage.setItem('userData', JSON.stringify(userData));
    setAccount(userData);
    setIsAuthenticated(true);

    const newSocket = socketService.connect(userData.userName);
    setSocket(newSocket);

    socketService.onOnlineUsers((users) => {
      setOnlineUsers(users);
    });

    socketService.onReceiveMessage((message) => {
      setMessages(prev => [...prev, message]);
    });

    socketService.onLoadMessages((loadedMessages) => {
      setMessages(loadedMessages);
    });
  };

  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('userData');
    setAccount(null);
    setIsAuthenticated(false);
    socketService.disconnect();
    setSocket(null);
    setOnlineUsers([]);
    setMessages([]);
  };

  return (
    <UserContext.Provider value={{
      account,
      setAccount,
      person,
      setPerson,
      socket,
      onlineUsers,
      messages,
      setMessages,
      isAuthenticated,
      login,
      logout
    }}>
      {children}
    </UserContext.Provider>
  );
};

export default UserProvider;