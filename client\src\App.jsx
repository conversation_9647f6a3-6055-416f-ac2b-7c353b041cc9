
import { useEffect } from 'react';
import './App.css'
// const { io } = require("socket.io-client");
import { io } from "socket.io-client";
const socket = io("http://localhost:3000");
function App() {
  useEffect(() => {
    socket.on("connect",(s)=>{
      console.log(s);
    })
    socket.on("welcome",(data)=>{
      console.log(data);
    })
  }, [])
  
  return (
    <div>
      hello
    </div>
  )
}

export default App
