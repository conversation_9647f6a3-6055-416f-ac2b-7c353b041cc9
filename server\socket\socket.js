const Message = require('../models/messageModel');

const initializeSocket = (server) => {
  const io = require('socket.io')(server);
  const onlineUsers = {};

  io.on('connection', (socket) => {
    socket.on('login', (username) => {
      onlineUsers[socket.id] = username;
      io.emit('onlineUsers', Object.values(onlineUsers));
      socket.emit('userId', socket.id);
    });

    socket.on('disconnect', () => {
      delete onlineUsers[socket.id];
      io.emit('onlineUsers', Object.values(onlineUsers));
    });

    socket.on('sendMessage', async ({ from, to, message }) => {
      // Save message to MongoDB
      await Message.create({ from, to, message });
      // Send message to recipient and sender
      const recipientSocketId = Object.keys(onlineUsers).find(id => onlineUsers[id] === to);
      if (recipientSocketId) {
        io.to(recipientSocketId).emit('receiveMessage', { from, message });
      }
      socket.emit('receiveMessage', { from, message });
    });

    socket.on('getMessages', async ({ user1, user2 }) => {
      // Fetch messages between two users
      const messages = await Message.find({
        $or: [
          { from: user1, to: user2 },
          { from: user2, to: user1 }
        ]
      }).sort('timestamp');
      socket.emit('loadMessages', messages);
    });
  });

  return io;
};

module.exports = initializeSocket;