* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON>o', sans-serif;
  background-color: #f0f2f5;
  height: 100vh;
}

.App {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.auth-card {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
}

.auth-card h2 {
  text-align: center;
  margin-bottom: 1.5rem;
  color: #333;
  font-size: 1.8rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e1e5e9;
  border-radius: 5px;
  font-size: 1rem;
  transition: border-color 0.3s;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
}

.error-message {
  color: #e74c3c;
  font-size: 0.9rem;
  margin-bottom: 1rem;
  text-align: center;
}

.success-message {
  color: #27ae60;
  font-size: 0.9rem;
  margin-bottom: 1rem;
  text-align: center;
}

button {
  width: 100%;
  padding: 0.75rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 1rem;
  cursor: pointer;
  transition: opacity 0.3s;
}

button:hover {
  opacity: 0.9;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.auth-card p {
  text-align: center;
  margin-top: 1rem;
  color: #666;
}

.auth-card a {
  color: #667eea;
  text-decoration: none;
}

.auth-card a:hover {
  text-decoration: underline;
}

.chat-container {
  display: flex;
  height: 100vh;
  background-color: #f0f2f5;
}

.chat-sidebar {
  width: 350px;
  background-color: white;
  border-right: 1px solid #e1e5e9;
  display: flex;
  flex-direction: column;
}

.chat-header {
  padding: 1rem;
  background-color: #00bfa5;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 1.2rem;
}

.username {
  font-weight: 500;
  font-size: 1rem;
}

.online-indicator {
  font-size: 0.8rem;
  padding: 2px 6px;
  border-radius: 10px;
}

.online-indicator.online {
  background-color: #4caf50;
  color: white;
}

.online-indicator.offline {
  background-color: #9e9e9e;
  color: white;
}

.logout-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9rem;
  width: auto;
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.users-list {
  flex: 1;
  overflow-y: auto;
}

.users-list h3 {
  padding: 1rem;
  color: #666;
  font-size: 1rem;
  border-bottom: 1px solid #e1e5e9;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  cursor: pointer;
  border-bottom: 1px solid #f0f2f5;
  transition: background-color 0.2s;
}

.user-item:hover {
  background-color: #f5f5f5;
}

.user-item.selected {
  background-color: #e3f2fd;
}

.user-details {
  margin-left: 0.75rem;
  flex: 1;
}

.user-details .username {
  display: block;
  margin-bottom: 0.25rem;
  color: #333;
}

.status {
  font-size: 0.8rem;
}

.status.online {
  color: #4caf50;
}

.status.offline {
  color: #9e9e9e;
}

.no-users {
  padding: 2rem;
  text-align: center;
  color: #666;
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #e5ddd5;
}

.chat-main .chat-header {
  background-color: #f0f2f5;
  color: #333;
  border-bottom: 1px solid #e1e5e9;
}

.no-chat-selected {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

.messages-container {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e5ddd5' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.message {
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
}

.message.sent {
  align-items: flex-end;
}

.message.received {
  align-items: flex-start;
}

.message-content {
  max-width: 70%;
  padding: 0.75rem 1rem;
  border-radius: 18px;
  word-wrap: break-word;
}

.message.sent .message-content {
  background-color: #dcf8c6;
  color: #333;
}

.message.received .message-content {
  background-color: white;
  color: #333;
}

.message-time {
  font-size: 0.7rem;
  color: #666;
  margin-top: 0.25rem;
}

.message-input-container {
  padding: 1rem;
  background-color: #f0f2f5;
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.message-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid #e1e5e9;
  border-radius: 25px;
  outline: none;
  font-size: 1rem;
}

.message-input:focus {
  border-color: #00bfa5;
}

.send-btn {
  background-color: #00bfa5;
  color: white;
  border: none;
  border-radius: 50%;
  width: 45px;
  height: 45px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

.send-btn:hover {
  background-color: #00a693;
}

@media (max-width: 768px) {
  .chat-container {
    flex-direction: column;
  }

  .chat-sidebar {
    width: 100%;
    height: 40vh;
  }

  .chat-main {
    height: 60vh;
  }
}