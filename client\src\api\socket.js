import io from 'socket.io-client';
import { SOCKET_URL } from '../config/config';

class SocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
  }

  connect(username) {
    if (!this.socket) {
      this.socket = io(SOCKET_URL);
      
      this.socket.on('connect', () => {
        this.isConnected = true;
        console.log('Connected to server');
        if (username) {
          this.login(username);
        }
      });

      this.socket.on('disconnect', () => {
        this.isConnected = false;
        console.log('Disconnected from server');
      });
    }
    return this.socket;
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }

  login(username) {
    if (this.socket) {
      this.socket.emit('login', username);
    }
  }

  sendMessage(messageData) {
    if (this.socket) {
      this.socket.emit('sendMessage', messageData);
    }
  }

  getMessages(users) {
    if (this.socket) {
      this.socket.emit('getMessages', users);
    }
  }

  onOnlineUsers(callback) {
    if (this.socket) {
      this.socket.on('onlineUsers', callback);
    }
  }

  onReceiveMessage(callback) {
    if (this.socket) {
      this.socket.on('receiveMessage', callback);
    }
  }

  onLoadMessages(callback) {
    if (this.socket) {
      this.socket.on('loadMessages', callback);
    }
  }

  offOnlineUsers() {
    if (this.socket) {
      this.socket.off('onlineUsers');
    }
  }

  offReceiveMessage() {
    if (this.socket) {
      this.socket.off('receiveMessage');
    }
  }

  offLoadMessages() {
    if (this.socket) {
      this.socket.off('loadMessages');
    }
  }

  getSocket() {
    return this.socket;
  }

  isSocketConnected() {
    return this.isConnected;
  }
}

const socketService = new SocketService();
export default socketService;
