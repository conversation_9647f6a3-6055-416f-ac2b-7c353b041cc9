const express = require("express");
const route = require("./routes/route");
const app = express();
require("dotenv").config();
const cors = require("cors");
const initializeSocket = require('./socket/socket');
const http = require('http');

const server = http.createServer(app);
initializeSocket(server);

app.use(express.json());
app.use(cors());

app.use("/api",route);

const dbConnect = require("./config/database");
dbConnect();

const PORT = process.env.PORT;

server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});