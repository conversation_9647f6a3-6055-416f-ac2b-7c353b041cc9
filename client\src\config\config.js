const config = {
  development: {
    API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api',
    SOCKET_URL: import.meta.env.VITE_SOCKET_URL || 'http://localhost:5000'
  },
  production: {
    API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'https://your-backend-domain.com/api',
    SOCKET_URL: import.meta.env.VITE_SOCKET_URL || 'https://your-backend-domain.com'
  }
};

const environment = import.meta.env.MODE || 'development';

export const API_BASE_URL = config[environment].API_BASE_URL;
export const SOCKET_URL = config[environment].SOCKET_URL;

export default config[environment];
