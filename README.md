# Real-Time Chat Application

A WhatsApp-like real-time chat application built with React, Node.js, Socket.io, and MongoDB.

## Features

- User authentication (Login/Signup)
- Real-time messaging
- Online/Offline status indicators
- WhatsApp-like UI design
- Responsive design for mobile and desktop

## Tech Stack

### Frontend
- React 19
- React Router DOM
- Socket.io Client
- Axios
- CSS3

### Backend
- Node.js
- Express.js
- Socket.io
- MongoDB with Mongoose
- JWT Authentication
- bcrypt for password hashing

## Installation

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (local or cloud)

### Backend Setup

1. Navigate to the server directory:
```bash
cd server
```

2. Install dependencies:
```bash
npm install
```

3. Create a `.env` file in the server directory:
```env
PORT=5000
MONGODB_URI=mongodb://localhost:27017/chatapp
JWT_SECRET=your-super-secret-jwt-key
```

4. Start the server:
```bash
npm start
```

### Frontend Setup

1. Navigate to the client directory:
```bash
cd client
```

2. Install dependencies:
```bash
npm install
```

3. Create a `.env` file in the client directory:
```env
VITE_API_BASE_URL=http://localhost:5000/api
VITE_SOCKET_URL=http://localhost:5000
```

4. Start the development server:
```bash
npm run dev
```

## Deployment

### Backend Deployment (Heroku/Railway/Render)

1. Set environment variables:
   - `PORT` (automatically set by most platforms)
   - `MONGODB_URI` (your MongoDB connection string)
   - `JWT_SECRET` (a secure random string)

2. Deploy using your preferred platform

### Frontend Deployment (Vercel/Netlify)

1. Update the `.env` file with your production backend URL:
```env
VITE_API_BASE_URL=https://your-backend-domain.com/api
VITE_SOCKET_URL=https://your-backend-domain.com
```

2. Build the project:
```bash
npm run build
```

3. Deploy the `dist` folder to your hosting platform

## Configuration

### Changing URLs for Deployment

All API and Socket URLs are centrally managed in `client/src/config/config.js`. You can:

1. Update the `.env` files with your production URLs
2. Or directly modify the config file for different environments

### Environment Variables

#### Client (.env)
- `VITE_API_BASE_URL`: Backend API base URL
- `VITE_SOCKET_URL`: Socket.io server URL

#### Server (.env)
- `PORT`: Server port (default: 5000)
- `MONGODB_URI`: MongoDB connection string
- `JWT_SECRET`: Secret key for JWT tokens

## Usage

1. Open the application in your browser
2. Sign up for a new account or login with existing credentials
3. See online users in the sidebar
4. Click on a user to start chatting
5. Send real-time messages
6. See online/offline status of users

## API Endpoints

- `POST /api/register` - User registration
- `POST /api/login` - User login

## Socket Events

- `login` - User connects and goes online
- `disconnect` - User disconnects and goes offline
- `sendMessage` - Send a message to another user
- `receiveMessage` - Receive a message from another user
- `getMessages` - Load chat history between two users
- `loadMessages` - Receive chat history
- `onlineUsers` - Get list of online users

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

This project is licensed under the MIT License.
