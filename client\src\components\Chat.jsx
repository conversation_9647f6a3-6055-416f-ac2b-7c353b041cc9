import React, { useState, useContext, useEffect, useRef } from 'react';
import { UserContext } from '../context/userContext';
import { useNavigate } from 'react-router-dom';

const Chat = () => {
  const { account, socket, onlineUsers, messages, setMessages, logout } = useContext(UserContext);
  const [selectedUser, setSelectedUser] = useState(null);
  const [messageInput, setMessageInput] = useState('');
  const [userMessages, setUserMessages] = useState([]);
  const messagesEndRef = useRef(null);
  const navigate = useNavigate();

  useEffect(() => {
    if (!account) {
      navigate('/login');
    }
  }, [account, navigate]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [userMessages]);

  useEffect(() => {
    if (selectedUser && socket) {
      socket.emit('getMessages', {
        user1: account.userName,
        user2: selectedUser
      });
    }
  }, [selectedUser, socket, account]);

  useEffect(() => {
    if (socket) {
      socket.on('loadMessages', (loadedMessages) => {
        setUserMessages(loadedMessages);
      });

      socket.on('receiveMessage', (message) => {
        if (message.from === selectedUser || message.from === account.userName) {
          setUserMessages(prev => [...prev, message]);
        }
      });
    }
  }, [socket, selectedUser, account]);

  const handleUserSelect = (user) => {
    if (user !== account.userName) {
      setSelectedUser(user);
      setUserMessages([]);
    }
  };

  const sendMessage = () => {
    if (messageInput.trim() && selectedUser && socket) {
      const messageData = {
        from: account.userName,
        to: selectedUser,
        message: messageInput.trim()
      };
      
      socket.emit('sendMessage', messageData);
      setMessageInput('');
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      sendMessage();
    }
  };

  const isUserOnline = (user) => {
    return onlineUsers.includes(user);
  };

  const otherUsers = onlineUsers.filter(user => user !== account?.userName);

  return (
    <div className="chat-container">
      <div className="chat-sidebar">
        <div className="chat-header">
          <div className="user-info">
            <div className="user-avatar">{account?.userName?.charAt(0).toUpperCase()}</div>
            <span className="username">{account?.userName}</span>
            <div className="online-indicator online"></div>
          </div>
          <button className="logout-btn" onClick={logout}>Logout</button>
        </div>
        
        <div className="users-list">
          <h3>Users</h3>
          {otherUsers.map((user, index) => (
            <div
              key={index}
              className={`user-item ${selectedUser === user ? 'selected' : ''}`}
              onClick={() => handleUserSelect(user)}
            >
              <div className="user-avatar">{user.charAt(0).toUpperCase()}</div>
              <div className="user-details">
                <span className="username">{user}</span>
                <div className={`online-indicator ${isUserOnline(user) ? 'online' : 'offline'}`}>
                  {isUserOnline(user) ? 'Online' : 'Offline'}
                </div>
              </div>
            </div>
          ))}
          {otherUsers.length === 0 && (
            <div className="no-users">No other users online</div>
          )}
        </div>
      </div>

      <div className="chat-main">
        {selectedUser ? (
          <>
            <div className="chat-header">
              <div className="user-avatar">{selectedUser.charAt(0).toUpperCase()}</div>
              <div className="user-details">
                <span className="username">{selectedUser}</span>
                <div className={`status ${isUserOnline(selectedUser) ? 'online' : 'offline'}`}>
                  {isUserOnline(selectedUser) ? 'Online' : 'Offline'}
                </div>
              </div>
            </div>
            
            <div className="messages-container">
              {userMessages.map((msg, index) => (
                <div
                  key={index}
                  className={`message ${msg.from === account.userName ? 'sent' : 'received'}`}
                >
                  <div className="message-content">{msg.message}</div>
                  <div className="message-time">
                    {new Date(msg.createdAt || msg.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>
            
            <div className="message-input-container">
              <input
                type="text"
                value={messageInput}
                onChange={(e) => setMessageInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Type a message..."
                className="message-input"
              />
              <button onClick={sendMessage} className="send-btn">Send</button>
            </div>
          </>
        ) : (
          <div className="no-chat-selected">
            <h3>Select a user to start chatting</h3>
          </div>
        )}
      </div>
    </div>
  );
};

export default Chat;
